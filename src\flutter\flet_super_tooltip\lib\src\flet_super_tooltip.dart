import 'package:flet/flet.dart';
import 'package:flutter/material.dart';
import 'package:super_tooltip/super_tooltip.dart';

class FletSuperTooltipControl extends StatefulWidget {
  final Control? parent;
  final Control control;
  final List<Control> children;
  final bool parentDisabled;
  final bool? parentAdaptive;
  final FletControlBackend backend;

  const FletSuperTooltipControl({
    super.key,
    required this.parent,
    required this.control,
    required this.children,
    required this.parentDisabled,
    required this.parentAdaptive,
    required this.backend,
  });

  @override
  State<FletSuperTooltipControl> createState() =>
      _FletSuperTooltipControlState();
}

class _FletSuperTooltipControlState extends State<FletSuperTooltipControl> {
  late SuperTooltipController _controller;

  @override
  void initState() {
    super.initState();
    _controller = SuperTooltipController();
    widget.backend.subscribeMethods(widget.control.id, _onMethodCall);
  }

  @override
  void dispose() {
    widget.backend.unsubscribeMethods(widget.control.id);
    _controller.dispose();
    super.dispose();
  }

  Future<String?> _onMethodCall(
      String methodName, Map<String, String> args) async {
    switch (methodName) {
      case "show_tooltip":
        await _controller.showTooltip();
        return null;
      case "hide_tooltip":
        await _controller.hideTooltip();
        return null;
      case "toggle_tooltip":
        if (_controller.isVisible) {
          await _controller.hideTooltip();
        } else {
          await _controller.showTooltip();
        }
        return null;
      default:
        return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("Building FletSuperTooltipControl");

    // Parse popup direction
    TooltipDirection popupDirection = _parsePopupDirection(
      widget.control.attrString("popup_direction", "down")!,
    );

    // Parse close button type
    CloseButtonType closeButtonType = _parseCloseButtonType(
      widget.control.attrString("close_button_type", "inside")!,
    );

    // Get content text
    String content = widget.control.attrString("content", "") ?? "";

    // Create the SuperTooltip widget
    Widget tooltip = SuperTooltip(
      controller: _controller,
      popupDirection: popupDirection,
      content: Text(
        content,
        style: const TextStyle(color: Colors.white),
      ),
      showCloseButton: widget.control.attrBool("show_close_button", false)!,
      closeButtonType: closeButtonType,
      closeButtonColor: parseColor(
          Theme.of(context), widget.control.attrString("close_button_color")),
      closeButtonSize: widget.control.attrDouble("close_button_size"),
      showBarrier: widget.control.attrBool("show_barrier"),
      barrierColor: parseColor(
          Theme.of(context), widget.control.attrString("barrier_color")),
      backgroundColor: parseColor(
          Theme.of(context), widget.control.attrString("background_color")),
      borderColor: parseColor(
              Theme.of(context), widget.control.attrString("border_color")) ??
          Colors.black,
      borderWidth: widget.control.attrDouble("border_width", 0.0)!,
      borderRadius: widget.control.attrDouble("border_radius", 10.0)!,
      arrowTipDistance: widget.control.attrDouble("arrow_tip_distance", 2.0)!,
      arrowBaseWidth: widget.control.attrDouble("arrow_base_width", 20.0)!,
      arrowLength: widget.control.attrDouble("arrow_length", 20.0)!,
      arrowTipRadius: widget.control.attrDouble("arrow_tip_radius", 0.0)!,
      shadowColor: parseColor(
          Theme.of(context), widget.control.attrString("shadow_color")),
      elevation: widget.control.attrDouble("elevation", 0.0)!,
      hasShadow: widget.control.attrBool("has_shadow"),
      shadowBlurRadius: widget.control.attrDouble("shadow_blur_radius"),
      shadowSpreadRadius: widget.control.attrDouble("shadow_spread_radius"),
      minimumOutsideMargin:
          widget.control.attrDouble("minimum_outside_margin", 20.0)!,
      verticalOffset: widget.control.attrDouble("vertical_offset", 0.0)!,
      fadeInDuration: Duration(
        milliseconds:
            widget.control.attrDouble("fade_in_duration", 150.0)!.toInt(),
      ),
      fadeOutDuration: Duration(
        milliseconds:
            widget.control.attrDouble("fade_out_duration", 0.0)!.toInt(),
      ),
      showDropBoxFilter:
          widget.control.attrBool("show_drop_box_filter", false)!,
      sigmaX: widget.control.attrDouble("sigma_x", 5.0)!,
      sigmaY: widget.control.attrDouble("sigma_y", 5.0)!,
      hideTooltipOnTap: widget.control.attrBool("hide_tooltip_on_tap", false)!,
      hideTooltipOnBarrierTap:
          widget.control.attrBool("hide_tooltip_on_barrier_tap", true)!,
      toggleOnTap: widget.control.attrBool("toggle_on_tap", false)!,
      showOnTap: widget.control.attrBool("show_on_tap", true)!,
      clickThrough: widget.control.attrBool("click_through", false)!,
      onShow: () {
        debugPrint("SuperTooltip onShow triggered");
        widget.backend.triggerControlEvent(widget.control.id, "show", "");
      },
      onHide: () {
        debugPrint("SuperTooltip onHide triggered");
        widget.backend.triggerControlEvent(widget.control.id, "hide", "");
      },
      onLongPress: () {
        debugPrint("SuperTooltip onLongPress triggered");
        widget.backend.triggerControlEvent(widget.control.id, "long_press", "");
      },
      child: widget.children.isNotEmpty
          ? createControl(
              widget.control, widget.children.first.id, widget.parentDisabled,
              parentAdaptive: widget.parentAdaptive)
          : Container(
              width: 40,
              height: 40,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.blue,
              ),
              child: const Icon(
                Icons.info,
                color: Colors.white,
              ),
            ),
    );

    return constrainedControl(context, tooltip, widget.parent, widget.control);
  }

  // Helper methods
  TooltipDirection _parsePopupDirection(String direction) {
    switch (direction.toLowerCase()) {
      case "up":
        return TooltipDirection.up;
      case "down":
        return TooltipDirection.down;
      case "left":
        return TooltipDirection.left;
      case "right":
        return TooltipDirection.right;
      default:
        return TooltipDirection.down;
    }
  }

  CloseButtonType _parseCloseButtonType(String type) {
    switch (type.toLowerCase()) {
      case "inside":
        return CloseButtonType.inside;
      case "outside":
        return CloseButtonType.outside;
      default:
        return CloseButtonType.inside;
    }
  }
}
