import flet as ft

from flet_super_tooltip import FletSuperTooltip, TooltipDirection, CloseButtonType


def main(page: ft.Page):
    page.title = "FletSuperTooltip Examples"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.scroll = ft.ScrollMode.AUTO

    # Event handlers
    def on_tooltip_show(e):
        print(f"Tooltip shown: {e.control}")

    def on_tooltip_hide(e):
        print(f"Tooltip hidden: {e.control}")

    def on_long_press(e):
        print(f"Long press detected: {e.control}")

    # Example 1: Basic tooltip with simple styling
    example1 = ft.Column([
        ft.Text("Example 1: Basic Tooltip", size=18, weight=ft.FontWeight.BOLD),
        ft.Text("Simple tooltip with basic styling", size=14, color=ft.Colors.GREY_700),
        ft.Container(
            content=FletSuperTooltip(
                child=ft.ElevatedButton(
                    text="Hover me!",
                    bgcolor=ft.Colors.BLUE,
                    color=ft.Colors.WHITE,
                ),
                content="This is a basic tooltip!",
                popup_direction=TooltipDirection.UP,
                background_color=ft.Colors.BLACK87,
                border_radius=8,
                on_show=on_tooltip_show,
                on_hide=on_tooltip_hide,
            ),
            margin=ft.margin.symmetric(vertical=10),
        )
    ])

    # Example 2: Tooltip with close button and custom colors
    example2 = ft.Column([
        ft.Text("Example 2: Tooltip with Close Button", size=18, weight=ft.FontWeight.BOLD),
        ft.Text("Tooltip with close button and custom styling", size=14, color=ft.Colors.GREY_700),
        ft.Container(
            content=FletSuperTooltip(
                child=ft.Container(
                    content=ft.Icon(ft.Icons.INFO, color=ft.Colors.WHITE, size=30),
                    width=60,
                    height=60,
                    bgcolor=ft.Colors.ORANGE,
                    border_radius=30,
                    alignment=ft.alignment.center,
                ),
                content=ft.Text("This tooltip has a close button and custom colors!"),
                popup_direction=TooltipDirection.RIGHT,
                show_close_button=True,
                close_button_type=CloseButtonType.INSIDE,
                close_button_color=ft.Colors.RED,
                close_button_size=20,
                background_color=ft.Colors.DEEP_PURPLE,
                border_color=ft.Colors.PURPLE,
                border_width=2,
                border_radius=15,
                on_show=on_tooltip_show,
                on_hide=on_tooltip_hide,
            ),
            margin=ft.margin.symmetric(vertical=10),
        )
    ])

    # Example 3: Tooltip with barrier and shadow effects
    example3 = ft.Column([
        ft.Text("Example 3: Tooltip with Barrier & Shadow", size=18, weight=ft.FontWeight.BOLD),
        ft.Text("Tooltip with barrier, shadow effects and custom arrow", size=14, color=ft.Colors.GREY_700),
        ft.Container(
            content=FletSuperTooltip(
                child=ft.Container(
                    content=ft.Text("Click me!", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                    width=120,
                    height=50,
                    bgcolor=ft.Colors.GREEN,
                    border_radius=25,
                    alignment=ft.alignment.center,
                ),
                content="This tooltip has barrier, shadow and custom arrow settings!",
                popup_direction=TooltipDirection.DOWN,
                show_barrier=True,
                barrier_color=ft.Colors.BLACK26,
                background_color=ft.Colors.TEAL,
                has_shadow=True,
                shadow_color=ft.Colors.BLACK54,
                elevation=8,
                shadow_blur_radius=10,
                shadow_spread_radius=2,
                arrow_base_width=30,
                arrow_length=15,
                arrow_tip_radius=5,
                arrow_tip_distance=5,
                on_show=on_tooltip_show,
                on_hide=on_tooltip_hide,
            ),
            margin=ft.margin.symmetric(vertical=10),
        )
    ])

    # Example 4: Tooltip with animations and blur effects
    example4 = ft.Column([
        ft.Text("Example 4: Animated Tooltip with Blur", size=18, weight=ft.FontWeight.BOLD),
        ft.Text("Tooltip with custom animations and blur effects", size=14, color=ft.Colors.GREY_700),
        ft.Container(
            content=FletSuperTooltip(
                content=ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.STAR, color=ft.Colors.YELLOW, size=25),
                        ft.Text("Animated", color=ft.Colors.WHITE, size=12),
                    ], alignment=ft.MainAxisAlignment.CENTER, spacing=5),
                    width=80,
                    height=80,
                    bgcolor=ft.Colors.INDIGO,
                    border_radius=40,
                    alignment=ft.alignment.center,
                ),
                popup_direction=TooltipDirection.LEFT,
                background_color=ft.Colors.PINK,
                border_radius=20,
                fade_in_duration=500,
                fade_out_duration=300,
                show_drop_box_filter=True,
                sigma_x=8,
                sigma_y=8,
                vertical_offset=10,
                minimum_outside_margin=30,
                on_show=on_tooltip_show,
                on_hide=on_tooltip_hide,
            ),
        )
    ])

    # Example 5: Interactive tooltip with long press and tap behaviors
    example5 = ft.Column([
        ft.Text("Example 5: Interactive Tooltip", size=18, weight=ft.FontWeight.BOLD),
        ft.Text("Tooltip with long press, toggle, and tap behaviors", size=14, color=ft.Colors.GREY_700),
        ft.Container(
            content=FletSuperTooltip(
                child=ft.Container(
                    content=ft.Text("Long press or tap!",
                                   color=ft.Colors.WHITE,
                                   text_align=ft.TextAlign.CENTER,
                                   size=12),
                    width=100,
                    height=60,
                    bgcolor=ft.Colors.RED_ACCENT,
                    border_radius=10,
                    alignment=ft.alignment.center,
                ),
                content="Long press detected! This tooltip toggles on tap and hides when you tap outside.",
                popup_direction=TooltipDirection.UP,
                background_color=ft.Colors.AMBER,
                border_color=ft.Colors.ORANGE,
                border_width=3,
                border_radius=12,
                show_close_button=True,
                close_button_type=CloseButtonType.OUTSIDE,
                close_button_color=ft.Colors.RED,
                toggle_on_tap=True,
                hide_tooltip_on_tap=False,
                hide_tooltip_on_barrier_tap=True,
                show_on_tap=True,
                on_show=on_tooltip_show,
                on_hide=on_tooltip_hide,
                on_long_press=on_long_press,
            ),
            margin=ft.margin.symmetric(vertical=10),
        )
    ])

    # Example 6: Tooltip with complex child widget
    example6 = ft.Column([
        ft.Text("Example 6: Complex Child Widget", size=18, weight=ft.FontWeight.BOLD),
        ft.Text("Tooltip with a complex child containing multiple elements", size=14, color=ft.Colors.GREY_700),
        ft.Container(
            content=FletSuperTooltip(
                child=ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.ListTile(
                                leading=ft.Icon(ft.Icons.PERSON, color=ft.Colors.BLUE),
                                title=ft.Text("User Profile", weight=ft.FontWeight.BOLD),
                                subtitle=ft.Text("Hover for details"),
                            ),
                            ft.Row([
                                ft.Icon(ft.Icons.EMAIL, size=16, color=ft.Colors.GREY),
                                ft.Text("<EMAIL>", size=12),
                            ], alignment=ft.MainAxisAlignment.CENTER),
                        ], spacing=5),
                        padding=10,
                    ),
                    elevation=4,
                    width=200,
                ),
                content="This is a detailed user profile card with comprehensive information and interactive elements!",
                popup_direction=TooltipDirection.RIGHT,
                background_color=ft.Colors.BLUE_GREY_800,
                border_color=ft.Colors.BLUE_GREY_600,
                border_width=1,
                border_radius=10,
                show_barrier=True,
                barrier_color=ft.Colors.BLACK12,
                has_shadow=True,
                elevation=6,
                arrow_base_width=25,
                arrow_length=12,
                on_show=on_tooltip_show,
                on_hide=on_tooltip_hide,
            ),
            margin=ft.margin.symmetric(vertical=10),
        )
    ])

    # Control buttons for programmatic tooltip control
    def show_all_tooltips():
        # This would require references to tooltip instances
        print("Show all tooltips programmatically")

    def hide_all_tooltips():
        # This would require references to tooltip instances
        print("Hide all tooltips programmatically")

    controls = ft.Row([
        ft.ElevatedButton("Show All Tooltips", on_click=lambda _: show_all_tooltips()),
        ft.ElevatedButton("Hide All Tooltips", on_click=lambda _: hide_all_tooltips()),
    ], alignment=ft.MainAxisAlignment.CENTER)

    # Main layout
    page.add(
        ft.Column([
            ft.Text("FletSuperTooltip Examples",
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER),
            ft.Text("Explore the versatility of FletSuperTooltip with these examples",
                    size=16,
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER),
            ft.Divider(height=20),
            example1,
            ft.Divider(),
            example2,
            ft.Divider(),
            example3,
            ft.Divider(),
            example4,
            ft.Divider(),
            example5,
            ft.Divider(),
            example6,
            ft.Divider(),
            controls,
        ], spacing=20)
    )


ft.app(main)
