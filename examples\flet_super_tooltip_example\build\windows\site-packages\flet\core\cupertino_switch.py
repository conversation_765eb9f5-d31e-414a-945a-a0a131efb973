from typing import Any, Optional, Union

from flet.core.animation import AnimationV<PERSON>ue
from flet.core.badge import BadgeValue
from flet.core.constrained_control import ConstrainedControl
from flet.core.control import OptionalNumber
from flet.core.ref import Ref
from flet.core.tooltip import TooltipValue
from flet.core.types import (
    ColorEnums,
    ColorValue,
    ControlStateValue,
    IconValue,
    LabelPosition,
    OffsetValue,
    OptionalControlEventCallable,
    ResponsiveNumber,
    RotateValue,
    ScaleValue,
)
from flet.utils.deprecated import deprecated_property


class CupertinoSwitch(ConstrainedControl):
    """
    An iOS-style switch. Used to toggle the on/off state of a single setting.

    Example:
    ```
    import flet as ft

    def main(page: ft.Page):
        page.add(
            ft.CupertinoSwitch(label="Cupertino Switch", value=True),
            ft.Switch(label="Material Checkbox", value=True),
            ft.Container(height=20),
            ft.Text(
                "Adaptive Switch shows as CupertinoSwitch on macOS and iOS and as Switch on other platforms:"
            ),
            ft.Switch(adaptive=True, label="Adaptive Switch", value=True),
        )

    ft.app(target=main)
    ```
    -----

    Online docs: https://flet.dev/docs/controls/cupertinoswitch
    """

    def __init__(
        self,
        label: Optional[str] = None,
        value: Optional[bool] = None,
        label_position: Optional[LabelPosition] = None,
        active_color: Optional[ColorValue] = None,
        thumb_color: Optional[ColorValue] = None,
        track_color: Optional[ColorValue] = None,
        focus_color: Optional[ColorValue] = None,
        autofocus: Optional[bool] = None,
        on_label_color: Optional[ColorValue] = None,
        off_label_color: Optional[ColorValue] = None,
        active_thumb_image: Optional[str] = None,
        inactive_thumb_image: Optional[str] = None,
        active_track_color: Optional[ColorValue] = None,
        inactive_thumb_color: Optional[ColorValue] = None,
        inactive_track_color: Optional[ColorValue] = None,
        track_outline_color: ControlStateValue[ColorValue] = None,
        track_outline_width: ControlStateValue[OptionalNumber] = None,
        thumb_icon: ControlStateValue[IconValue] = None,
        on_change: OptionalControlEventCallable = None,
        on_focus: OptionalControlEventCallable = None,
        on_blur: OptionalControlEventCallable = None,
        on_image_error: OptionalControlEventCallable = None,
        #
        # ConstrainedControl
        #
        ref: Optional[Ref] = None,
        key: Optional[str] = None,
        width: OptionalNumber = None,
        height: OptionalNumber = None,
        left: OptionalNumber = None,
        top: OptionalNumber = None,
        right: OptionalNumber = None,
        bottom: OptionalNumber = None,
        expand: Union[None, bool, int] = None,
        expand_loose: Optional[bool] = None,
        col: Optional[ResponsiveNumber] = None,
        opacity: OptionalNumber = None,
        rotate: Optional[RotateValue] = None,
        scale: Optional[ScaleValue] = None,
        offset: Optional[OffsetValue] = None,
        aspect_ratio: OptionalNumber = None,
        animate_opacity: Optional[AnimationValue] = None,
        animate_size: Optional[AnimationValue] = None,
        animate_position: Optional[AnimationValue] = None,
        animate_rotation: Optional[AnimationValue] = None,
        animate_scale: Optional[AnimationValue] = None,
        animate_offset: Optional[AnimationValue] = None,
        on_animation_end: OptionalControlEventCallable = None,
        tooltip: Optional[TooltipValue] = None,
        badge: Optional[BadgeValue] = None,
        visible: Optional[bool] = None,
        disabled: Optional[bool] = None,
        data: Any = None,
    ):
        ConstrainedControl.__init__(
            self,
            ref=ref,
            key=key,
            width=width,
            height=height,
            left=left,
            top=top,
            right=right,
            bottom=bottom,
            expand=expand,
            expand_loose=expand_loose,
            col=col,
            opacity=opacity,
            rotate=rotate,
            scale=scale,
            offset=offset,
            aspect_ratio=aspect_ratio,
            animate_opacity=animate_opacity,
            animate_size=animate_size,
            animate_position=animate_position,
            animate_rotation=animate_rotation,
            animate_scale=animate_scale,
            animate_offset=animate_offset,
            on_animation_end=on_animation_end,
            tooltip=tooltip,
            badge=badge,
            visible=visible,
            disabled=disabled,
            data=data,
        )
        self.value = value
        self.label = label
        self.label_position = label_position
        self.autofocus = autofocus
        self.active_color = active_color
        self.focus_color = focus_color
        self.thumb_color = thumb_color
        self.track_color = track_color
        self.on_change = on_change
        self.on_focus = on_focus
        self.on_blur = on_blur
        self.on_label_color = on_label_color
        self.off_label_color = off_label_color
        self.active_thumb_image = active_thumb_image
        self.active_track_color = active_track_color
        self.inactive_thumb_color = inactive_thumb_color
        self.inactive_track_color = inactive_track_color
        self.track_outline_color = track_outline_color
        self.track_outline_width = track_outline_width
        self.thumb_icon = thumb_icon
        self.inactive_thumb_image = inactive_thumb_image
        self.on_image_error = on_image_error

    def _get_control_name(self):
        return "cupertinoswitch"

    def before_update(self):
        super().before_update()
        self._set_attr_json("thumbColor", self.__thumb_color)
        self._set_attr_json("trackColor", self.__track_color)
        self._set_attr_json(
            "trackOutlineColor", self.__track_outline_color, wrap_attr_dict=True
        )
        self._set_attr_json(
            "trackOutlineWidth", self.__track_outline_width, wrap_attr_dict=True
        )
        self._set_attr_json("thumbIcon", self.__thumb_icon, wrap_attr_dict=True)

    # value
    @property
    def value(self) -> bool:
        return self._get_attr("value", data_type="bool", def_value=False)

    @value.setter
    def value(self, value: Optional[bool]):
        self._set_attr("value", value)

    # label
    @property
    def label(self) -> Optional[str]:
        return self._get_attr("label")

    @label.setter
    def label(self, value: Optional[str]):
        self._set_attr("label", value)

    # active_thumb_image
    @property
    def active_thumb_image(self) -> Optional[str]:
        return self._get_attr("activeThumbImage")

    @active_thumb_image.setter
    def active_thumb_image(self, value: Optional[str]):
        self._set_attr("activeThumbImage", value)

    # inactive_thumb_image
    @property
    def inactive_thumb_image(self) -> Optional[str]:
        return self._get_attr("inactiveThumbImage")

    @inactive_thumb_image.setter
    def inactive_thumb_image(self, value: Optional[str]):
        self._set_attr("inactiveThumbImage", value)

    # active_track_color
    @property
    def active_track_color(self) -> Optional[ColorValue]:
        return self.__active_track_color

    @active_track_color.setter
    def active_track_color(self, value: Optional[ColorValue]):
        self.__active_track_color = value
        self._set_enum_attr("activeTrackColor", value, ColorEnums)

    # inactive_track_color
    @property
    def inactive_track_color(self) -> Optional[ColorValue]:
        return self.__inactive_track_color

    @inactive_track_color.setter
    def inactive_track_color(self, value: Optional[ColorValue]):
        self.__inactive_track_color = value
        self._set_enum_attr("inactiveTrackColor", value, ColorEnums)

    # track_outline_color
    @property
    def track_outline_color(self) -> ControlStateValue[ColorValue]:
        return self.__track_outline_color

    @track_outline_color.setter
    def track_outline_color(self, value: ControlStateValue[ColorValue]):
        self.__track_outline_color = value

    # track_outline_width
    @property
    def track_outline_width(self) -> ControlStateValue[OptionalNumber]:
        return self.__track_outline_width

    @track_outline_width.setter
    def track_outline_width(self, value: ControlStateValue[OptionalNumber]):
        self.__track_outline_width = value

    # thumb_icon
    @property
    def thumb_icon(self) -> ControlStateValue[IconValue]:
        return self.__thumb_icon

    @thumb_icon.setter
    def thumb_icon(self, value: ControlStateValue[IconValue]):
        self.__thumb_icon = value

    # label_position
    @property
    def label_position(self) -> Optional[LabelPosition]:
        return self.__label_position

    @label_position.setter
    def label_position(self, value: Optional[LabelPosition]):
        self.__label_position = value
        self._set_enum_attr("labelPosition", value, LabelPosition)

    # autofocus
    @property
    def autofocus(self) -> bool:
        return self._get_attr("autofocus", data_type="bool", def_value=False)

    @autofocus.setter
    def autofocus(self, value: Optional[bool]):
        self._set_attr("autofocus", value)

    # active_color
    @property
    def active_color(self) -> Optional[ColorValue]:
        deprecated_property(
            name="active_color",
            reason="Use active_track_color instead.",
            version="0.26.0",
            delete_version="0.29.0",
        )
        return self.__active_color

    @active_color.setter
    def active_color(self, value: Optional[ColorValue]):
        self.__active_color = value
        self._set_enum_attr("activeColor", value, ColorEnums)
        if value is not None:
            deprecated_property(
                name="active_color",
                reason="Use active_track_color instead.",
                version="0.26.0",
                delete_version="0.29.0",
            )

    # focus_color
    @property
    def focus_color(self) -> Optional[ColorValue]:
        return self.__focus_color

    @focus_color.setter
    def focus_color(self, value: Optional[ColorValue]):
        self.__focus_color = value
        self._set_enum_attr("focusColor", value, ColorEnums)

    # thumb_color
    @property
    def thumb_color(self) -> Optional[ColorValue]:
        return self.__thumb_color

    @thumb_color.setter
    def thumb_color(self, value: Optional[ColorValue]):
        self.__thumb_color = value

    # track_color
    @property
    def track_color(self) -> Optional[ColorValue]:
        deprecated_property(
            name="track_color",
            reason="Use inactive_track_color instead.",
            version="0.26.0",
            delete_version="0.29.0",
        )
        return self.__track_color

    @track_color.setter
    def track_color(self, value: Optional[ColorValue]):
        self.__track_color = value
        if value is not None:
            deprecated_property(
                name="track_color",
                reason="Use inactive_track_color instead.",
                version="0.26.0",
                delete_version="0.29.0",
            )

    # on_label_color
    @property
    def on_label_color(self) -> Optional[ColorValue]:
        return self.__on_label_color

    @on_label_color.setter
    def on_label_color(self, value: Optional[ColorValue]):
        self.__on_label_color = value
        self._set_enum_attr("onLabelColor", value, ColorEnums)

    # off_label_color
    @property
    def off_label_color(self) -> Optional[ColorValue]:
        return self.__off_label_color

    @off_label_color.setter
    def off_label_color(self, value: Optional[ColorValue]):
        self.__off_label_color = value
        self._set_enum_attr("offLabelColor", value, ColorEnums)

    # on_change
    @property
    def on_change(self) -> OptionalControlEventCallable:
        return self._get_event_handler("change")

    @on_change.setter
    def on_change(self, handler: OptionalControlEventCallable):
        self._add_event_handler("change", handler)

    # on_focus
    @property
    def on_focus(self) -> OptionalControlEventCallable:
        return self._get_event_handler("focus")

    @on_focus.setter
    def on_focus(self, handler: OptionalControlEventCallable):
        self._add_event_handler("focus", handler)

    # on_blur
    @property
    def on_blur(self) -> OptionalControlEventCallable:
        return self._get_event_handler("blur")

    @on_blur.setter
    def on_blur(self, handler: OptionalControlEventCallable):
        self._add_event_handler("blur", handler)

    # on_image_error
    @property
    def on_image_error(self) -> OptionalControlEventCallable:
        return self._get_event_handler("image_error")

    @on_image_error.setter
    def on_image_error(self, handler: OptionalControlEventCallable):
        self._add_event_handler("image_error", handler)
