[project]
name = "flet-super-tooltip-example"
version = "1.0.0"
description = ""
readme = "README.md"
requires-python = ">=3.9"
authors = [
    { name = "Flet developer", email = "<EMAIL>" }
]
dependencies = [
  "flet-super-tooltip",
  "flet>=0.28.3",
]

[tool.flet]
# org name in reverse domain name notation, e.g. "com.mycompany".
# Combined with project.name to build bundle ID for iOS and Android apps
org = "com.mycompany"

# project display name that is used as an app title on Android and iOS home screens,
# shown in window titles and about app dialogs on desktop.
product = "flet-super-tooltip-example"

# company name to display in about app dialogs
company = "Flet"

# copyright text to display in about app dialogs
copyright = "Copyright (C) 2025 by MyCompany"

[tool.flet.app]
path = "src"

[tool.flet.dev_packages]
flet-super-tooltip = "..\\.." # relative path

[tool.uv]
dev-dependencies = [
    "flet[all]==0.28.3",
]

[tool.uv.sources]
flet-super-tooltip = { path = "..\\..\\", editable = true }

[tool.poetry.group.dev.dependencies]
flet = {extras = ["all"], version = "0.28.3"}
flet-super-tooltip = {path = "..\\..\\", develop = true}