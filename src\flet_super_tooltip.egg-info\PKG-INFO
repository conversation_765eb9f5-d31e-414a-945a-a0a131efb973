Metadata-Version: 2.4
Name: flet-super-tooltip
Version: 0.1.0
Summary: FletSuperTooltip control for Flet
Author-email: Flet contributors <<EMAIL>>
Project-URL: Homepage, https://mydomain.dev
Project-URL: Documentation, https://github.com/MyGithubAccount/flet-super-tooltip
Project-URL: Repository, https://github.com/MyGithubAccount/flet-super-tooltip
Project-URL: Issues, https://github.com/MyGithubAccount/flet-super-tooltip/issues
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Requires-Dist: flet>=0.28.3

# flet-super-tooltip
FletSuperTooltip control for Flet

## Installation

Add dependency to `pyproject.toml` of your Flet app:

* **Git dependency**

Link to git repository:

```
dependencies = [
  "flet-super-tooltip @ git+https://github.com/MyGithubAccount/flet-super-tooltip",
  "flet>=0.28.3",
]
```

* **PyPi dependency**  

If the package is published on pypi.org:

```
dependencies = [
  "flet-super-tooltip",
  "flet>=0.28.3",
]
```

Build your app:
```
flet build macos -v
```

## Documentation

[Link to documentation](https://MyGithubAccount.github.io/flet-super-tooltip/)
