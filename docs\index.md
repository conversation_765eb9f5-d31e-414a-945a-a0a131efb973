# Introduction

FletSuperTooltip for Flet.

## Examples

```
import flet as ft

from flet_super_tooltip import FletSuperTooltip


def main(page: ft.Page):
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER

    page.add(

                ft.Container(height=150, width=300, alignment = ft.alignment.center, bgcolor=ft.Colors.PURPLE_200, content=FletSuperTooltip(
                    tooltip="My new FletSuperTooltip Control tooltip",
                    value = "My new FletSuperTooltip Flet Control", 
                ),),

    )


ft.app(main)
```

## Classes

[FletSuperTooltip](FletSuperTooltip.md)


